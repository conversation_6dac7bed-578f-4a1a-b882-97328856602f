import { render, screen } from '@testing-library/react'
import Home from '../src/app/page'
import LeaderboardsPage from '../src/app/leaderboards/page'
import BracketsPage from '../src/app/brackets/page'
import DashboardPage from '../src/app/dashboard/page'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return '/'
  },
}))

describe('Page Components', () => {
  describe('Home Page', () => {
    it('renders the main heading', () => {
      render(<Home />)
      
      const heading = screen.getByText('Real-Time Streaming Analytics')
      expect(heading).toBeInTheDocument()
    })

    it('renders navigation links', () => {
      render(<Home />)
      
      expect(screen.getByText('Leaderboards')).toBeInTheDocument()
      expect(screen.getByText('Brackets')).toBeInTheDocument()
      expect(screen.getByText('Dashboard')).toBeInTheDocument()
    })

    it('renders feature cards', () => {
      render(<Home />)
      
      expect(screen.getByText('Live Analytics')).toBeInTheDocument()
      expect(screen.getByText('Tournament Tracking')).toBeInTheDocument()
      expect(screen.getByText('Smart Alerts')).toBeInTheDocument()
    })

    it('renders system status section', () => {
      render(<Home />)
      
      expect(screen.getByText('System Status')).toBeInTheDocument()
      expect(screen.getByText('Data Sources')).toBeInTheDocument()
      expect(screen.getByText('Twitch API')).toBeInTheDocument()
      expect(screen.getByText('YouTube API')).toBeInTheDocument()
      expect(screen.getByText('PandaScore API')).toBeInTheDocument()
    })
  })

  describe('Leaderboards Page', () => {
    it('renders the leaderboards heading', () => {
      render(<LeaderboardsPage />)
      
      const heading = screen.getByText('Live Stream Leaderboards')
      expect(heading).toBeInTheDocument()
    })

    it('renders platform tabs', () => {
      render(<LeaderboardsPage />)
      
      expect(screen.getByText('Twitch')).toBeInTheDocument()
      expect(screen.getByText('YouTube')).toBeInTheDocument()
      expect(screen.getByText('Combined')).toBeInTheDocument()
    })

    it('renders mock stream data', () => {
      render(<LeaderboardsPage />)
      
      // Check for mock Twitch streamers
      expect(screen.getByText('Ninja')).toBeInTheDocument()
      expect(screen.getByText('Shroud')).toBeInTheDocument()
      expect(screen.getByText('xQc')).toBeInTheDocument()
      
      // Check for mock YouTube channels
      expect(screen.getByText('PewDiePie')).toBeInTheDocument()
      expect(screen.getByText('MrBeast Gaming')).toBeInTheDocument()
    })

    it('renders platform statistics', () => {
      render(<LeaderboardsPage />)
      
      expect(screen.getByText('Platform Statistics')).toBeInTheDocument()
      expect(screen.getByText('Total Twitch Viewers')).toBeInTheDocument()
      expect(screen.getByText('Total YouTube Viewers')).toBeInTheDocument()
      expect(screen.getByText('Active Streams')).toBeInTheDocument()
    })
  })

  describe('Brackets Page', () => {
    it('renders the brackets heading', () => {
      render(<BracketsPage />)
      
      const heading = screen.getByText('Tournament Brackets')
      expect(heading).toBeInTheDocument()
    })

    it('renders mock tournament data', () => {
      render(<BracketsPage />)
      
      expect(screen.getByText('World Championship 2024')).toBeInTheDocument()
      expect(screen.getByText('Masters Tournament')).toBeInTheDocument()
      expect(screen.getByText('Spring Split Finals')).toBeInTheDocument()
    })

    it('renders tournament progress bars', () => {
      render(<BracketsPage />)

      expect(screen.getAllByText('Tournament Progress')).toHaveLength(3)
    })

    it('renders sample bracket', () => {
      render(<BracketsPage />)
      
      expect(screen.getByText('Sample Bracket - World Championship 2024')).toBeInTheDocument()
      expect(screen.getByText('Quarter Finals')).toBeInTheDocument()
      expect(screen.getByText('Semi Finals')).toBeInTheDocument()
      expect(screen.getByText('Finals')).toBeInTheDocument()
    })
  })

  describe('Dashboard Page', () => {
    it('renders the dashboard heading', () => {
      render(<DashboardPage />)

      const heading = screen.getByRole('heading', { name: 'Dashboard' })
      expect(heading).toBeInTheDocument()
    })

    it('renders quick stats cards', () => {
      render(<DashboardPage />)
      
      expect(screen.getByText('Active Streams')).toBeInTheDocument()
      expect(screen.getByText('Live Tournaments')).toBeInTheDocument()
      expect(screen.getByText('Total Viewers')).toBeInTheDocument()
      expect(screen.getByText('Uptime')).toBeInTheDocument()
    })

    it('renders API status section', () => {
      render(<DashboardPage />)
      
      expect(screen.getByText('API Status')).toBeInTheDocument()
      expect(screen.getByText('Database')).toBeInTheDocument()
    })

    it('renders admin actions', () => {
      render(<DashboardPage />)
      
      expect(screen.getByText('Admin Actions')).toBeInTheDocument()
      expect(screen.getByText('🔄 Trigger Data Sync')).toBeInTheDocument()
    })

    it('renders API endpoints section', () => {
      render(<DashboardPage />)
      
      expect(screen.getByText('API Endpoints')).toBeInTheDocument()
      expect(screen.getByText('/api/leaderboards/twitch')).toBeInTheDocument()
      expect(screen.getByText('/api/leaderboards/youtube')).toBeInTheDocument()
    })
  })
})
