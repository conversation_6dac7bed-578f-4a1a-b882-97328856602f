import mongoose, { Document, Schema } from 'mongoose';

export interface ITwitchStream extends Document {
  id: string;
  user_id: string;
  user_login: string;
  user_name: string;
  game_id: string;
  game_name: string;
  type: string;
  title: string;
  viewer_count: number;
  started_at: Date;
  language: string;
  thumbnail_url: string;
  tag_ids: string[];
  is_mature: boolean;
  created_at: Date;
  updated_at: Date;
}

const TwitchStreamSchema: Schema = new Schema({
  id: { type: String, required: true, unique: true },
  user_id: { type: String, required: true },
  user_login: { type: String, required: true },
  user_name: { type: String, required: true },
  game_id: { type: String, required: true },
  game_name: { type: String, required: true },
  type: { type: String, required: true },
  title: { type: String, required: true },
  viewer_count: { type: Number, required: true },
  started_at: { type: Date, required: true },
  language: { type: String, required: true },
  thumbnail_url: { type: String, required: true },
  tag_ids: [{ type: String }],
  is_mature: { type: Boolean, default: false },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

TwitchStreamSchema.index({ user_id: 1 });
TwitchStreamSchema.index({ game_id: 1 });
TwitchStreamSchema.index({ viewer_count: -1 });
TwitchStreamSchema.index({ started_at: -1 });

export default mongoose.models.TwitchStream || mongoose.model<ITwitchStream>('TwitchStream', TwitchStreamSchema);
