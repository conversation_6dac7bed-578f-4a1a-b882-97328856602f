import mongoose, { Document, Schema } from 'mongoose';

export interface IYouTubeStream extends Document {
  id: string;
  channel_id: string;
  channel_title: string;
  title: string;
  description: string;
  published_at: Date;
  thumbnail_url: string;
  viewer_count: number;
  like_count: number;
  comment_count: number;
  category_id: string;
  live_broadcast_content: string;
  duration: string;
  tags: string[];
  created_at: Date;
  updated_at: Date;
}

const YouTubeStreamSchema: Schema = new Schema({
  id: { type: String, required: true, unique: true },
  channel_id: { type: String, required: true },
  channel_title: { type: String, required: true },
  title: { type: String, required: true },
  description: { type: String, required: true },
  published_at: { type: Date, required: true },
  thumbnail_url: { type: String, required: true },
  viewer_count: { type: Number, default: 0 },
  like_count: { type: Number, default: 0 },
  comment_count: { type: Number, default: 0 },
  category_id: { type: String, required: true },
  live_broadcast_content: { type: String, required: true },
  duration: { type: String, default: '' },
  tags: [{ type: String }],
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

YouTubeStreamSchema.index({ channel_id: 1 });
YouTubeStreamSchema.index({ viewer_count: -1 });
YouTubeStreamSchema.index({ published_at: -1 });

export default mongoose.models.YouTubeStream || mongoose.model<IYouTubeStream>('YouTubeStream', YouTubeStreamSchema);
