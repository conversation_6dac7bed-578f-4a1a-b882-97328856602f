import mongoose, { Document, Schema } from 'mongoose';

export interface ITournament extends Document {
  id: number;
  name: string;
  slug: string;
  description: string;
  game: string;
  status: 'not_started' | 'running' | 'finished' | 'cancelled';
  begin_at: Date;
  end_at: Date;
  prize_pool: string;
  tier: string;
  winner_id: number;
  winner_type: string;
  league: {
    id: number;
    name: string;
    slug: string;
  };
  serie: {
    id: number;
    name: string;
    slug: string;
  };
  teams: Array<{
    id: number;
    name: string;
    slug: string;
    acronym: string;
    image_url: string;
  }>;
  matches: number[];
  created_at: Date;
  updated_at: Date;
}

const TournamentSchema: Schema = new Schema({
  id: { type: Number, required: true, unique: true },
  name: { type: String, required: true },
  slug: { type: String, required: true },
  description: { type: String, default: '' },
  game: { type: String, required: true },
  status: { 
    type: String, 
    enum: ['not_started', 'running', 'finished', 'cancelled'],
    default: 'not_started'
  },
  begin_at: { type: Date, required: true },
  end_at: { type: Date },
  prize_pool: { type: String, default: '' },
  tier: { type: String, default: '' },
  winner_id: { type: Number },
  winner_type: { type: String },
  league: {
    id: { type: Number, required: true },
    name: { type: String, required: true },
    slug: { type: String, required: true }
  },
  serie: {
    id: { type: Number, required: true },
    name: { type: String, required: true },
    slug: { type: String, required: true }
  },
  teams: [{
    id: { type: Number, required: true },
    name: { type: String, required: true },
    slug: { type: String, required: true },
    acronym: { type: String, required: true },
    image_url: { type: String, default: '' }
  }],
  matches: [{ type: Number }],
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

TournamentSchema.index({ game: 1 });
TournamentSchema.index({ status: 1 });
TournamentSchema.index({ begin_at: -1 });

export default mongoose.models.Tournament || mongoose.model<ITournament>('Tournament', TournamentSchema);
