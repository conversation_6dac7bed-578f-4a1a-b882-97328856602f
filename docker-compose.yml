version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: streaming-app-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: streaming-app
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - streaming-app-network

  nextjs:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: streaming-app-nextjs
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=*********************************************************************
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=development-secret-key
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - mongodb
    networks:
      - streaming-app-network
    command: npm run dev

  # Redis for future use
  redis:
    image: redis:7.2-alpine
    container_name: streaming-app-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - streaming-app-network

volumes:
  mongodb_data:
  redis_data:

networks:
  streaming-app-network:
    driver: bridge
