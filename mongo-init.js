// MongoDB initialization script
db = db.getSiblingDB('streaming-app');

// Create collections
db.createCollection('twitchstreams');
db.createCollection('youtubestreams');
db.createCollection('tournaments');
db.createCollection('matches');
db.createCollection('users');
db.createCollection('watchlists');
db.createCollection('alerts');

// Create indexes for better performance
db.twitchstreams.createIndex({ "user_id": 1 });
db.twitchstreams.createIndex({ "game_id": 1 });
db.twitchstreams.createIndex({ "viewer_count": -1 });
db.twitchstreams.createIndex({ "started_at": -1 });

db.youtubestreams.createIndex({ "channel_id": 1 });
db.youtubestreams.createIndex({ "viewer_count": -1 });
db.youtubestreams.createIndex({ "published_at": -1 });

db.tournaments.createIndex({ "game": 1 });
db.tournaments.createIndex({ "status": 1 });
db.tournaments.createIndex({ "begin_at": -1 });

db.matches.createIndex({ "tournament_id": 1 });
db.matches.createIndex({ "status": 1 });
db.matches.createIndex({ "scheduled_at": -1 });

db.users.createIndex({ "email": 1 }, { unique: true });
db.watchlists.createIndex({ "user_id": 1 });
db.alerts.createIndex({ "user_id": 1 });

print('Database initialized successfully!');
