# 🎮 Streaming & eSports Metrics Hub

A full-featured Next.js application for tracking live streams, tournament brackets, and eSports metrics across multiple platforms including Twitch, YouTube, and major gaming tournaments.

## 🚀 Features

- **Real-time Stream Analytics**: Track live streams from Twitch and YouTube with viewer counts and engagement metrics
- **Tournament Brackets**: Interactive tournament brackets with live match results and schedules
- **Data Ingestion**: Automated data fetching from multiple APIs using scheduled cron jobs
- **REST API**: Comprehensive API endpoints for accessing streaming and tournament data
- **Responsive Dashboard**: Modern UI built with Next.js 14, TypeScript, and Tailwind CSS
- **Database Integration**: MongoDB with Mongoose for data persistence
- **Docker Support**: Full containerization with Docker Compose for easy deployment

## 🏗️ Architecture

### Tech Stack
- **Frontend**: Next.js 14 (App Router), TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Node.js
- **Database**: MongoDB with Mongoose ODM
- **Scheduling**: node-cron for automated data fetching
- **Containerization**: Docker & Docker Compose
- **Authentication**: NextAuth.js (ready for GitHub/Email providers)

### Project Structure
```
├── src/app/                 # Next.js App Router pages and API routes
│   ├── api/                 # API endpoints
│   │   ├── cron/           # Data ingestion cron jobs
│   │   ├── leaderboards/   # Stream leaderboard APIs
│   │   └── brackets/       # Tournament bracket APIs
│   ├── leaderboards/       # Leaderboard pages
│   ├── brackets/           # Tournament bracket pages
│   └── dashboard/          # Admin dashboard
├── models/                 # Mongoose schemas
├── lib/                    # Utility functions and database connection
├── components/             # Reusable React components
├── public/                 # Static assets
└── docker-compose.yml      # Docker services configuration
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Docker and Docker Compose
- MongoDB (or use Docker Compose)

### 1. Clone and Setup
```bash
git clone <repository-url>
cd StreamingApp
npm install
```

### 2. Environment Configuration
Copy the example environment file and configure your settings:
```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:
```env
# Database
MONGODB_URI=mongodb://localhost:27017/streaming-app

# NextAuth.js
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# API Keys (optional for development)
TWITCH_CLIENT_ID=your-twitch-client-id
TWITCH_CLIENT_SECRET=your-twitch-client-secret
YOUTUBE_API_KEY=your-youtube-api-key
PANDASCORE_API_KEY=your-pandascore-api-key

# Cron Settings
CRON_ENABLED=true
DATA_FETCH_INTERVAL=*/5 * * * *
```

### 3. Start with Docker Compose (Recommended)
```bash
docker-compose up
```

This will start:
- Next.js application on http://localhost:3000
- MongoDB on localhost:27017
- Redis on localhost:6379 (for future use)

### 4. Alternative: Local Development
```bash
# Start MongoDB separately
# Then run the Next.js app
npm run dev
```

## 📊 API Endpoints

### Leaderboards
- `GET /api/leaderboards/twitch` - Twitch stream leaderboard
- `GET /api/leaderboards/youtube` - YouTube stream leaderboard

### Tournament Brackets
- `GET /api/brackets/[tournamentId]` - Tournament bracket data

### Data Management
- `GET /api/cron` - Trigger manual data refresh
- `POST /api/cron` - Programmatic data refresh

### Query Parameters
Most endpoints support filtering and pagination:
```
/api/leaderboards/twitch?limit=50&game=Fortnite&language=en
/api/leaderboards/youtube?limit=25&category=20&sortBy=viewer_count
```

## 🗄️ Database Models

### TwitchStream
- Stream metadata, viewer counts, game information
- Indexed by user_id, game_id, viewer_count, started_at

### YouTubeStream
- Video metadata, engagement metrics, channel information
- Indexed by channel_id, viewer_count, published_at

### Tournament
- Tournament details, teams, matches, prize pools
- Indexed by game, status, begin_at

### User
- User accounts, preferences, API keys, subscriptions
- Indexed by email, api_key

## 🔄 Data Ingestion

The application uses scheduled cron jobs to fetch data from external APIs:

- **Twitch Helix API**: Live streams, games, user data
- **YouTube Data API v3**: Live broadcasts, video statistics
- **PandaScore API**: Tournament schedules, match results

Data is automatically fetched every 5 minutes (configurable) and upserted into MongoDB.

## 🐳 Docker Deployment

### Development
```bash
docker-compose up
```

### Production
```bash
docker-compose -f docker-compose.prod.yml up -d
```

The application includes:
- Multi-stage Docker build for optimized production images
- Health checks and restart policies
- Volume mounts for data persistence
- Network isolation between services

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run integration tests
npm run test:integration
```

## 📈 Monitoring & Logging

- **Logging**: Structured logging with Pino/Winston
- **Metrics**: Prometheus metrics endpoint at `/metrics`
- **Health Checks**: Built-in health check endpoints
- **Error Tracking**: Ready for integration with error tracking services

## 🔐 Security

- Environment variable validation
- API rate limiting
- Input sanitization and validation
- CORS configuration
- NextAuth.js for authentication
- API key management for users

## 🚀 Deployment Options

### Vercel (Recommended)
```bash
npm run build
vercel deploy
```

### Docker Container Registry
```bash
docker build -t streaming-app .
docker push your-registry/streaming-app
```

### Traditional Hosting
```bash
npm run build
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Troubleshooting

### Common Issues

**MongoDB Connection Issues**
```bash
# Check if MongoDB is running
docker-compose ps
# View MongoDB logs
docker-compose logs mongodb
```

**API Rate Limiting**
- Check your API keys and quotas
- Adjust the `DATA_FETCH_INTERVAL` in environment variables

**Build Issues**
```bash
# Clear Next.js cache
rm -rf .next
npm run build
```

### Getting Help

- Check the [Issues](https://github.com/your-repo/issues) page
- Review the [API Documentation](http://localhost:3000/api-docs)
- Join our [Discord Community](https://discord.gg/your-server)

## 🗺️ Roadmap

- [ ] Real-time WebSocket updates
- [ ] Advanced analytics and charts
- [ ] Mobile app companion
- [ ] Machine learning predictions
- [ ] Multi-language support
- [ ] Advanced notification system
- [ ] Embeddable widgets
- [ ] GraphQL API
- [ ] Performance optimizations
- [ ] Enhanced security features

---

Built with ❤️ using Next.js, TypeScript, and modern web technologies.
