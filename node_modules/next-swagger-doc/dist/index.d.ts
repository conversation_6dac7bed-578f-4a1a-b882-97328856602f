import { NextApiRequest, NextApiResponse } from 'next';
import { Options, OAS3Definition } from 'swagger-jsdoc';

type SwaggerOptions = Options & {
    apiFolder?: string;
    schemaFolders?: string[];
    definition: OAS3Definition;
    outputFile?: string;
};
/**
 * Create swagger JSON
 * @param options.openApiVersion Open API version {3.0.0}
 * @param options.apiFolder NextJS API folder {pages/api}
 * @param options.schemaFolders entity schema folders
 * @param options.title Title
 * @param options.version Version
 * @returns Swagger JSON Spec
 */
declare function createSwaggerSpec({ apiFolder, schemaFolders, ...swaggerOptions }?: SwaggerOptions): object;
/**
 * WithSwagger middleware
 * @param options.openApiVersion Open API version {3.0.0}
 * @param options.apiFolder NextJS API folder {pages/api}
 * @param options.schemaFolders entity schema folders
 * @param options.title Title
 * @param options.version Version
 * @returns
 */
declare function withSwagger({ apiFolder, schemaFolders, ...swaggerOptions }?: SwaggerOptions): () => (_req: NextApiRequest, res: NextApiResponse) => void;

export { type SwaggerOptions, createSwaggerSpec, withSwagger };
