'use strict';

var fs = require('fs');
var cleye = require('cleye');
var swagger = require('./swagger-cYUwHqdb.cjs');
require('node:path');
require('swagger-jsdoc');

const argv = cleye.cli({
  name: "next-swagger-doc-cli",
  // Define parameters
  // Becomes available in ._.filePath
  parameters: [
    "<config file>"
    // Next swagger config file is required
  ],
  // Define flags/options
  // Becomes available in .flags
  flags: {
    // Parses `--output` as a string
    output: {
      type: String,
      description: "Output file path",
      default: "public/swagger.json"
    }
  }
});
const config = fs.readFileSync(argv._.configFile);
const spec = swagger.createSwaggerSpec(
  JSON.parse(config.toString())
);
console.log(
  `Generating swagger spec to ${argv.flags.output} with config`,
  config.toString()
);
fs.writeFileSync(argv.flags.output, JSON.stringify(spec, null, 2));
