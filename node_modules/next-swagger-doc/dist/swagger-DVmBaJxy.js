import { join } from 'node:path';
import swaggerJsdoc from 'swagger-jsdoc';

const defaultOptions = {
  apiFolder: "pages/api",
  schemaFolders: [],
  definition: {
    openapi: "3.0.0",
    info: {
      title: "Next Swagger Doc Demo Api",
      version: "1.0"
    }
  }
};
function createSwaggerSpec({
  apiFolder = "pages/api",
  schemaFolders = [],
  ...swaggerOptions
} = defaultOptions) {
  const scanFolders = [apiFolder, ...schemaFolders];
  const apis = scanFolders.flatMap((folder) => {
    const buildApiDirectory = join(process.cwd(), ".next/server", folder);
    const apiDirectory = join(process.cwd(), folder);
    const publicDirectory = join(process.cwd(), "public");
    const fileTypes = ["ts", "tsx", "jsx", "js", "json", "swagger.yaml"];
    return [
      ...fileTypes.map((fileType) => `${apiDirectory}/**/*.${fileType}`),
      // Only scan build directory for *.swagger.yaml and *.js files
      ...["js", "swagger.yaml", "json"].map(
        (fileType) => `${buildApiDirectory}/**/*.${fileType}`
      ),
      // Support load static files from public directory
      ...["swagger.yaml", "json"].map(
        (fileType) => `${publicDirectory}/**/*.${fileType}`
      )
    ];
  });
  const definition = {
    ...swaggerOptions.definition,
    ...process.env.__NEXT_ROUTER_BASEPATH && !swaggerOptions.definition.servers && {
      servers: [
        {
          url: process.env.__NEXT_ROUTER_BASEPATH,
          description: "next-js"
        }
      ]
    }
  };
  const options = {
    apis,
    // Files containing annotations as above
    ...swaggerOptions,
    definition
  };
  const spec = swaggerJsdoc(options);
  return spec;
}
function withSwagger({
  apiFolder = "pages/api",
  schemaFolders = [],
  ...swaggerOptions
} = defaultOptions) {
  return () => (_req, res) => {
    try {
      const swaggerSpec = createSwaggerSpec({
        apiFolder,
        schemaFolders,
        ...swaggerOptions
      });
      res.status(200).send(swaggerSpec);
    } catch (error) {
      res.status(400).send(error);
    }
  };
}

export { createSwaggerSpec as c, withSwagger as w };
