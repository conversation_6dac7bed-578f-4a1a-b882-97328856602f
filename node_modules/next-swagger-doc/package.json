{"name": "next-swagger-doc", "version": "0.4.1", "keywords": ["next-swagger", "next-swagger-doc", "nextjs", "swagger", "swagger-jsdoc"], "repository": {"type": "git", "url": "https://github.com/jellydn/next-swagger-doc"}, "license": "MIT", "author": "<PERSON><PERSON><PERSON>", "type": "module", "exports": {"require": "./dist/index.cjs", "import": "./dist/index.js"}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "typings": "dist/index.d.ts", "bin": {"next-swagger-doc-cli": "dist/cli.js"}, "files": ["dist", "src"], "dependencies": {"@types/swagger-jsdoc": "6.0.4", "cleye": "1.3.2", "isarray": "2.0.5", "swagger-jsdoc": "6.2.8"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@skypack/package-check": "0.2.2", "@types/node": "22.8.4", "@vitest/coverage-v8": "2.1.4", "@vitest/ui": "2.1.4", "all-contributors-cli": "6.26.1", "c8": "10.1.2", "next": "15.0.2", "pkgroll": "2.5.1", "size-limit": "11.1.6", "sort-package-json": "2.10.1", "tslib": "2.8.0", "typedoc": "0.26.10", "typescript": "5.6.3", "vite": "5.4.10", "vitest": "2.1.4"}, "peerDependencies": {"next": ">=9"}, "engines": {"node": ">=18"}, "scripts": {"build": "pkgroll", "coverage": "vitest run --coverage", "format": "biome format src", "lint": "biome lint src", "start": "pkgroll --watch", "test": "vitest run", "test:ui": "vitest --ui", "vercel-build": "npx typedoc src/index.ts"}}