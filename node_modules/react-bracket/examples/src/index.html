<!doctype html>
<head>
	<meta charset="utf-8">
	<title>React-Bracket Example</title>
	<meta name="viewport" content="initial-scale=1.0,user-scalable=no,maximum-scale=1,width=device-width">
	<meta name="keywords" content="react,reactjs,react component,component,bracket,control,ui,javascript">
	<meta name="description" content="React-Bracket is a bracket chart component for React.">
	<meta property="og:locale" content="en-us">
	<meta property="og:title" content="React-Bracket">
	<meta property="og:description" content="React-Bracket is a bracket chart component for React.">
	<meta property="og:url" content="https://github.com/derbystuff/react-bracket">
	<meta property="og:site_name" content="React-Bracket">
	<meta property="og:type" content="article">
	<link rel="stylesheet" href="example.css">
</head>
<body>
	<div class="container">
		<h1>React Bracket</h1>
		<h3><a href="https://github.com/derbystuff/react-bracket">View project and documentation on GitHub</a></h3>
		<h2>Examples:</h2>
    <div id="example"></div>
		<div class="footer">
			Copyright &copy; Jeremy Darling 2014. MIT Licensed.
		</div>
	</div>
	<script src="common.js"></script>
	<script src="bundle.js"></script>
	<script src="app.js"></script>
</body>
