.bracket{
  .rounds{
    display: flex;
    flex-direction: row;
    .round{
      display: flex;
      flex-direction: column;
      justify-content: center;

      .spacer{
        flex-grow: 1;
      }
      .spacer:first-child,
      .spacer:last-child{
        flex-grow: 0.5;
      }

      .heat{
        display: flex;
        flex-grow: 1;
        flex-direction: column;

        .placeholder-filler,
        .filler{
          min-height: 40px;
          flex-grow: 1;
        }
        .filler{
          border-right: 1px solid #aaa;
        }

        .participant-filler{
          display: flex;
          flex-grow: 1;
        }
        .participant-filler:first-child,
        .participant-filler:last-child{
          flex-grow: 0.5;
        }

        .participant-top,
        .participant-center,
        .participant-bottom{
        }
        .participant-top,
        .participant-center{
          border-bottom: 1px solid #aaa;
        }
        .participant-center{
        }
        .participant-bottom{
          border-top: 1px solid #aaa;
        }
      }
    }
  }
}
