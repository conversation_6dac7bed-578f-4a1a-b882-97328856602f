!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t;t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,t.Bracket=e()}}(function(){return function e(t,a,n){function r(l,c){if(!a[l]){if(!t[l]){var s="function"==typeof require&&require;if(!c&&s)return s(l,!0);if(i)return i(l,!0);var o=new Error("Cannot find module '"+l+"'");throw o.code="MODULE_NOT_FOUND",o}var p=a[l]={exports:{}};t[l][0].call(p.exports,function(e){var a=t[l][1][e];return r(a?a:e)},p,p.exports,e,t,a,n)}return a[l].exports}for(var i="function"==typeof require&&require,l=0;l<n.length;l++)r(n[l]);return r}({1:[function(e,t,a){(function(a){"use strict";var n="undefined"!=typeof window?window.React:"undefined"!=typeof a?a.React:null,r=e("classnames"),i=function(e,t){var a=t,n=(e.props.className||"").split(/[ \t]+/g);return n.forEach(function(e){a[e]=!0}),r(a)},l=function(){var e={};return Array.prototype.slice.call(arguments).forEach(function(t){e=Object.keys(t).reduce(function(e,a){return e[a]=t[a],e},e)}),e},c=n.createClass({displayName:"Bracket",getParticipant:function(e){if(this.props.getParticipant){var t=this.props.getParticipant(l(e,{participants:this.props.participants,data:this.props.data}));return n.createElement("span",{className:"participant",key:e.key},t)}return n.createElement("span",{className:"participant"},"getParticipant property not defined")},getRound:function(e){var t=e.key,a=e.heats,r=e.level,i=e.isFinal;if(i)return n.createElement("div",{className:"round",key:t},this.getFinal({key:t,level:r,winner:a[0]}));var a=a.map(function(e,t){return null===e?[this.getHeatPlaceholder({key:t,level:r}),n.createElement("div",{className:"spacer",key:t+"-spacer"})]:[this.getHeat({info:e,level:r,key:t}),n.createElement("div",{className:"spacer",key:t+"-spacer"})]}.bind(this));return n.createElement("div",{className:"round",key:t},n.createElement("div",{className:"spacer"}),a)},getHeatPlaceholder:function(e){var t=e.key;return n.createElement("div",{className:"heat",key:t},n.createElement("div",{className:"placeholder-top"}," "),n.createElement("div",{className:"placeholder-filler"}),n.createElement("div",{className:"placeholder-bottom"}," "))},getHeat:function(e){var t=e.info,a=e.key,r=e.level;return n.createElement("div",{className:"heat",key:a},n.createElement("div",{className:"participant-top"},this.getParticipant({level:r,info:t,index:0,placement:"top"})),n.createElement("div",{className:"filler"}),n.createElement("div",{className:"participant-bottom"},this.getParticipant({level:r,info:t,index:1,placement:"bottom"})))},getFinal:function(e){var t=e.winner,a=e.key;return n.createElement("div",{className:"heat"},n.createElement("div",{className:"participant-filler"}),n.createElement("div",{className:"participant-center"},this.getParticipant({level:a,info:t,index:0,placement:"winner",isFinal:!0})),n.createElement("div",{className:"participant-filler"}))},render:function(){var e=this.props.layout||[],t=e.length-1,a=e.map(function(e,a){return this.getRound({heats:e,key:a,level:a,isFinal:a===t})}.bind(this)),r=i(this,{bracket:!0});return n.createElement("div",{className:r},n.createElement("div",{className:"rounds"},a))}});t.exports=c}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{classnames:void 0}]},{},[1])(1)});