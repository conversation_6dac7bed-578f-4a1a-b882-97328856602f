.bracket .rounds {
  display: flex;
  flex-direction: row;
}
.bracket .rounds .round {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.bracket .rounds .round .spacer {
  flex-grow: 1;
}
.bracket .rounds .round .spacer:first-child,
.bracket .rounds .round .spacer:last-child {
  flex-grow: 0.5;
}
.bracket .rounds .round .heat {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
}
.bracket .rounds .round .heat .placeholder-filler,
.bracket .rounds .round .heat .filler {
  min-height: 40px;
  flex-grow: 1;
}
.bracket .rounds .round .heat .filler {
  border-right: 1px solid #aaa;
}
.bracket .rounds .round .heat .participant-filler {
  display: flex;
  flex-grow: 1;
}
.bracket .rounds .round .heat .participant-filler:first-child,
.bracket .rounds .round .heat .participant-filler:last-child {
  flex-grow: 0.5;
}
.bracket .rounds .round .heat .participant-top,
.bracket .rounds .round .heat .participant-center {
  border-bottom: 1px solid #aaa;
}
.bracket .rounds .round .heat .participant-bottom {
  border-top: 1px solid #aaa;
}
