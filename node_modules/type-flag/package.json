{"name": "type-flag", "version": "3.0.0", "description": "Typed command-line arguments parser", "keywords": ["cli", "command-line", "flag", "argv", "arguments", "parser", "typed", "typescript"], "license": "MIT", "repository": "privatenumber/type-flag", "funding": "https://github.com/privatenumber/type-flag?sponsor=1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "files": ["dist"], "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {"require": "./dist/index.js", "import": "./dist/index.mjs", "types": "./dist/index.d.ts"}, "imports": {"#type-flag": {"types": "./src/index.ts", "development": "./src/index.ts", "default": "./dist/index.mjs"}}}