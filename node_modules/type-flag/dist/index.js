"use strict";Object.defineProperty(exports,"__esModule",{value:!0});const O="known-flag",V="unknown-flag",k="argument",{stringify:h}=JSON,v=/\B([A-Z])/g,C=t=>t.replace(v,"-$1").toLowerCase(),{hasOwnProperty:D}=Object.prototype,w=(t,n)=>D.call(t,n),L=t=>Array.isArray(t),b=t=>typeof t=="function"?[t,!1]:L(t)?[t[0],!0]:b(t.type),A=(t,n)=>t===Boolean?n!=="false":n,m=(t,n)=>typeof n=="boolean"?n:t===Number&&n===""?Number.NaN:t(n),_=/[\s.:=]/,R=t=>{const n=`Flag name ${h(t)}`;if(t.length===0)throw new Error(`${n} cannot be empty`);if(t.length===1)throw new Error(`${n} must be longer than a character`);const r=t.match(_);if(r)throw new Error(`${n} cannot contain ${h(r?.[0])}`)},B=t=>{const n={},r=(e,o)=>{if(w(n,e))throw new Error(`Duplicate flags named ${h(e)}`);n[e]=o};for(const e in t){if(!w(t,e))continue;R(e);const o=t[e],s=[[],...b(o),o];r(e,s);const i=C(e);if(e!==i&&r(i,s),"alias"in o&&typeof o.alias=="string"){const{alias:a}=o,l=`Flag alias ${h(a)} for flag ${h(e)}`;if(a.length===0)throw new Error(`${l} cannot be empty`);if(a.length>1)throw new Error(`${l} must be a single character`);r(a,s)}}return n},K=(t,n)=>{const r={};for(const e in t){if(!w(t,e))continue;const[o,,s,i]=n[e];if(o.length===0&&"default"in i){let{default:a}=i;typeof a=="function"&&(a=a()),r[e]=a}else r[e]=s?o:o.pop()}return r},F="--",j=/[.:=]/,G=/^-{1,2}\w/,N=t=>{if(!G.test(t))return;const n=!t.startsWith(F);let r=t.slice(n?1:2),e;const o=r.match(j);if(o){const{index:s}=o;e=r.slice(s+1),r=r.slice(0,s)}return[r,e,n]},$=(t,{onFlag:n,onArgument:r})=>{let e;const o=(s,i)=>{if(typeof e!="function")return!0;e(s,i),e=void 0};for(let s=0;s<t.length;s+=1){const i=t[s];if(i===F){o();const l=t.slice(s+1);r?.(l,[s],!0);break}const a=N(i);if(a){if(o(),!n)continue;const[l,f,g]=a;if(g)for(let c=0;c<l.length;c+=1){o();const u=c===l.length-1;e=n(l[c],u?f:void 0,[s,c+1,u])}else e=n(l,f,[s])}else o(i,[s])&&r?.([i],[s])}o()},E=(t,n)=>{for(const[r,e,o]of n.reverse()){if(e){const s=t[r];let i=s.slice(0,e);if(o||(i+=s.slice(e+1)),i!=="-"){t[r]=i;continue}}t.splice(r,1)}},T=(t,n=process.argv.slice(2),{ignore:r}={})=>{const e=[],o=B(t),s={},i=[];return i[F]=[],$(n,{onFlag(a,l,f){const g=w(o,a);if(!r?.(g?O:V,a,l)){if(g){const[c,u]=o[a],y=A(u,l),p=(P,d)=>{e.push(f),d&&e.push(d),c.push(m(u,P||""))};return y===void 0?p:p(y)}w(s,a)||(s[a]=[]),s[a].push(l===void 0?!0:l),e.push(f)}},onArgument(a,l,f){r?.(k,n[l[0]])||(i.push(...a),f?(i[F]=a,n.splice(l[0])):e.push(l))}}),E(n,e),{flags:K(t,o),unknownFlags:s,_:i}},U=(t,n,r=process.argv.slice(2))=>{const e=t.split(",").map(l=>N(l)?.[0]),[o,s]=b(n),i=[],a=[];return $(r,{onFlag(l,f,g){if(!e.includes(l)||!s&&i.length>0)return;const c=A(o,f),u=(y,p)=>{a.push(g),p&&a.push(p),i.push(m(o,y||""))};return c===void 0?u:u(c)}}),E(r,a),s?i:i[0]};exports.getFlag=U,exports.typeFlag=T;
