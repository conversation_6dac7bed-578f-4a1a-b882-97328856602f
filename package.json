{"name": "streaming-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/mongodb-adapter": "^3.10.0", "@types/bcryptjs": "^2.4.6", "@types/node-cron": "^3.0.11", "bcryptjs": "^3.0.2", "mongoose": "^8.16.3", "next": "15.3.5", "next-auth": "^4.24.11", "next-swagger-doc": "^0.4.1", "node-cron": "^4.2.1", "pino": "^9.7.0", "react": "^19.0.0", "react-bracket": "^0.0.4", "react-dom": "^19.0.0", "recharts": "^3.1.0", "winston": "^3.17.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}