# Database
MONGODB_URI=mongodb://localhost:27017/streaming-app
MONGODB_DB=streaming-app

# NextAuth.js
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# GitHub OAuth (for NextAuth)
GITHUB_ID=your-github-client-id
GITHUB_SECRET=your-github-client-secret

# API Keys
TWITCH_CLIENT_ID=your-twitch-client-id
TWITCH_CLIENT_SECRET=your-twitch-client-secret
YOUTUBE_API_KEY=your-youtube-api-key
PANDASCORE_API_KEY=your-pandascore-api-key

# Notifications
FIREBASE_SERVER_KEY=your-firebase-server-key
SENDGRID_API_KEY=your-sendgrid-api-key
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number

# Cron Job Settings
CRON_ENABLED=true
DATA_FETCH_INTERVAL=*/5 * * * *

# App Settings
NODE_ENV=development
PORT=3000
LOG_LEVEL=info

# Redis (for future use)
REDIS_URL=redis://localhost:6379

# Monitoring
PROMETHEUS_ENABLED=false
